const app = require('./app');
const { initializeArabicFonts } = require('./services/arabicFontService');

const PORT = process.env.PORT || 3001;

// Initialize Almarai font support for Arabic-only system
initializeArabicFonts().then((success) => {
  if (success) {
    console.log('✓ تم تهيئة خط الماراي بنجاح');
  } else {
    console.warn('⚠ فشل في تهيئة خط الماراي - استخدام الخطوط الاحتياطية');
  }
});

const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`الخادم يعمل على المنفذ ${PORT}`);
  console.log(`البيئة: ${process.env.NODE_ENV || 'development'}`);
  console.log(`فحص الحالة: http://localhost:${PORT}/health`);
  console.log(`فحص الحالة: http://127.0.0.1:${PORT}/health`);
  console.log(`فحص الحالة: http://0.0.0.0:${PORT}/health`);
  console.log('🇸🇦 نظام التوقيع الإلكتروني العربي جاهز');

// Configure server timeouts for large file uploads
server.timeout = 10 * 60 * 1000; // 10 minutes
server.keepAliveTimeout = 5 * 60 * 1000; // 5 minutes
server.headersTimeout = 6 * 60 * 1000; // 6 minutes

  // Test server is actually listening
  const http = require('http');
  setTimeout(() => {
    const req = http.get(`http://localhost:${PORT}/health`, (res) => {
      console.log('✅ Self-test successful - server is responding');
    });
    req.on('error', (err) => {
      console.error('❌ Self-test failed:', err.message);
    });
  }, 1000);
});
