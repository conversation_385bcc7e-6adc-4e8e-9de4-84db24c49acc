const express = require('express');
const { 
  upload, 
  uploadSignature, 
  getSignatures, 
  getSignature, 
  deleteSignature 
} = require('../controllers/signatureController');
const { authenticateToken } = require('../middleware/auth');
const { requirePermission } = require('../middleware/roleAuth');

const router = express.Router();

// All signature routes require authentication
router.use(authenticateToken);

// Upload signature (admin only)
router.post('/upload', requirePermission('upload_signatures'), upload, uploadSignature);

// Get all signatures for user
router.get('/', getSignatures);

// Get specific signature
router.get('/:signatureId', getSignature);

// Delete signature (admin only)
router.delete('/:signatureId', requirePermission('upload_signatures'), deleteSignature);

module.exports = router;
