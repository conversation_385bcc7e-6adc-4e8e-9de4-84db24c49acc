import React from 'react';
import { useAuth } from '../services/AuthContext';

const AuthDebug: React.FC = () => {
  const { user, hasPermission, isAdmin } = useAuth();

  const testPermissions = [
    'sign_documents',
    'view_history',
    'upload_signatures',
    'verify_documents',
    'manage_users',
    'view_dashboard',
    'change_password'
  ];

  return (
    <div className="fixed top-4 right-4 bg-white border-2 border-red-500 p-4 rounded-lg shadow-lg z-50 max-w-md" dir="rtl">
      <h3 className="text-lg font-bold text-red-600 mb-3 font-['Almarai']">
        🔍 Debug: Authentication State
      </h3>
      
      <div className="space-y-2 text-sm">
        <div className="bg-gray-100 p-2 rounded">
          <strong>User Data:</strong>
          <pre className="text-xs mt-1 overflow-x-auto">
            {user ? JSON.stringify(user, null, 2) : 'No user data'}
          </pre>
        </div>

        <div className="bg-gray-100 p-2 rounded">
          <strong>Role Check:</strong>
          <div>Role: {user?.role || 'undefined'}</div>
          <div>isAdmin(): {isAdmin() ? 'true' : 'false'}</div>
        </div>

        <div className="bg-gray-100 p-2 rounded">
          <strong>Permissions:</strong>
          {testPermissions.map(permission => (
            <div key={permission} className="flex justify-between">
              <span>{permission}:</span>
              <span className={hasPermission(permission) ? 'text-green-600' : 'text-red-600'}>
                {hasPermission(permission) ? '✅' : '❌'}
              </span>
            </div>
          ))}
        </div>

        <div className="bg-gray-100 p-2 rounded">
          <strong>Navigation Visibility:</strong>
          <div className="flex justify-between">
            <span>توقيع المستندات:</span>
            <span className={hasPermission('sign_documents') ? 'text-green-600' : 'text-red-600'}>
              {hasPermission('sign_documents') ? 'VISIBLE' : 'HIDDEN'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>السجل:</span>
            <span className={hasPermission('view_history') && isAdmin() ? 'text-green-600' : 'text-red-600'}>
              {hasPermission('view_history') && isAdmin() ? 'VISIBLE' : 'HIDDEN'}
            </span>
          </div>
        </div>

        <div className="bg-gray-100 p-2 rounded">
          <strong>LocalStorage:</strong>
          <div>Token: {localStorage.getItem('token') ? 'Present' : 'Missing'}</div>
          <div>User: {localStorage.getItem('user') ? 'Present' : 'Missing'}</div>
        </div>
      </div>
    </div>
  );
};

export default AuthDebug;
