<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Auth State</title>
    <style>
        body {
            font-family: 'Almarai', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Authentication State</h1>
        
        <div class="section info">
            <h3>Current Status</h3>
            <div id="status">Not tested yet</div>
        </div>

        <div class="section">
            <h3>Actions</h3>
            <button onclick="loginAsUser()">Login as Regular User</button>
            <button onclick="loginAsAdmin()">Login as Admin</button>
            <button onclick="checkAuthState()">Check Auth State</button>
            <button onclick="testPermissions()">Test Permissions</button>
            <button onclick="clearAuth()">Clear Auth</button>
        </div>

        <div class="section">
            <h3>Results</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `section ${type}`;
            status.textContent = message;
        }

        async function loginAsUser() {
            try {
                updateStatus('Logging in as regular user...', 'warning');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    log(`✅ Regular User Login Successful:
Email: ${data.user.email}
Role: ${data.user.role}
ID: ${data.user.id}
Token: ${data.token.substring(0, 20)}...`, 'success');
                    
                    updateStatus('Logged in as regular user', 'success');
                } else {
                    log(`❌ Login Failed: ${JSON.stringify(data, null, 2)}`, 'error');
                    updateStatus('Login failed', 'error');
                }
            } catch (error) {
                log(`❌ Login Error: ${error.message}`, 'error');
                updateStatus('Login error', 'error');
            }
        }

        async function loginAsAdmin() {
            try {
                updateStatus('Logging in as admin...', 'warning');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    log(`✅ Admin Login Successful:
Email: ${data.user.email}
Role: ${data.user.role}
ID: ${data.user.id}
Token: ${data.token.substring(0, 20)}...`, 'success');
                    
                    updateStatus('Logged in as admin', 'success');
                } else {
                    log(`❌ Login Failed: ${JSON.stringify(data, null, 2)}`, 'error');
                    updateStatus('Login failed', 'error');
                }
            } catch (error) {
                log(`❌ Login Error: ${error.message}`, 'error');
                updateStatus('Login error', 'error');
            }
        }

        function checkAuthState() {
            const token = localStorage.getItem('token');
            const userStr = localStorage.getItem('user');
            
            if (!token || !userStr) {
                log('❌ No authentication data found in localStorage', 'error');
                updateStatus('Not authenticated', 'error');
                return;
            }

            try {
                const user = JSON.parse(userStr);
                
                log(`✅ Authentication State:
Token: ${token.substring(0, 20)}...
User Email: ${user.email}
User Role: ${user.role}
User ID: ${user.id}

LocalStorage Contents:
- token: ${token ? 'Present' : 'Missing'}
- user: ${userStr ? 'Present' : 'Missing'}`, 'success');

                updateStatus(`Authenticated as ${user.email} (${user.role})`, 'success');
            } catch (error) {
                log(`❌ Error parsing user data: ${error.message}`, 'error');
                updateStatus('Invalid auth data', 'error');
            }
        }

        function testPermissions() {
            const userStr = localStorage.getItem('user');
            
            if (!userStr) {
                log('❌ No user data found', 'error');
                return;
            }

            try {
                const user = JSON.parse(userStr);
                
                // Simulate the frontend permission logic
                const permissions = {
                    admin: [
                        'upload_signatures',
                        'verify_documents',
                        'sign_documents',
                        'view_history',
                        'manage_users',
                        'view_dashboard',
                        'change_password'
                    ],
                    user: [
                        'view_history',
                        'view_dashboard',
                        'change_password'
                    ]
                };

                const userPermissions = permissions[user.role] || [];
                const hasSignDocuments = userPermissions.includes('sign_documents');
                const hasViewHistory = userPermissions.includes('view_history');
                const isAdmin = user.role === 'admin';

                log(`🔍 Permission Check Results:
User Role: ${user.role}
Available Permissions: ${userPermissions.join(', ')}

Specific Checks:
- hasPermission('sign_documents'): ${hasSignDocuments}
- hasPermission('view_history'): ${hasViewHistory}
- isAdmin(): ${isAdmin}

Navigation Visibility:
- "توقيع المستندات" link: ${hasSignDocuments ? 'VISIBLE' : 'HIDDEN'}
- "السجل" link: ${hasViewHistory && isAdmin ? 'VISIBLE' : 'HIDDEN'}`, 'info');

                if (user.role === 'user' && hasSignDocuments) {
                    log('🚨 SECURITY ISSUE: Regular user has sign_documents permission!', 'error');
                }
            } catch (error) {
                log(`❌ Error testing permissions: ${error.message}`, 'error');
            }
        }

        function clearAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            log('🗑️ Authentication data cleared', 'warning');
            updateStatus('Not authenticated', 'error');
        }

        // Auto-check auth state on page load
        window.onload = function() {
            checkAuthState();
        };
    </script>
</body>
</html>
