const { query } = require('../models/database');

/**
 * Middleware to check if user has required role
 * @param {string|string[]} requiredRoles - Required role(s) to access the route
 */
const requireRole = (requiredRoles) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.userId;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'غير مخول للوصول'
        });
      }

      // Get user role from database
      const result = await query('SELECT role FROM users WHERE id = $1', [userId]);
      
      if (result.rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'المستخدم غير موجود'
        });
      }

      const userRole = result.rows[0].role;
      
      // Convert requiredRoles to array if it's a string
      const rolesArray = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
      
      // Check if user has required role
      if (!rolesArray.includes(userRole)) {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية للوصول إلى هذه الميزة'
        });
      }

      // Add user role to request object for use in controllers
      req.user.role = userRole;
      next();
      
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في التحقق من الصلاحيات'
      });
    }
  };
};

/**
 * Middleware to check if user is admin
 */
const requireAdmin = requireRole('admin');

/**
 * Middleware to check if user is admin or regular user (any authenticated user)
 */
const requireUser = requireRole(['admin', 'user']);

/**
 * Middleware to add user role to request without restricting access
 */
const addUserRole = async (req, res, next) => {
  try {
    const userId = req.user?.userId;
    
    if (userId) {
      const result = await query('SELECT role FROM users WHERE id = $1', [userId]);
      if (result.rows.length > 0) {
        req.user.role = result.rows[0].role;
      }
    }
    
    next();
  } catch (error) {
    console.error('Add user role error:', error);
    // Don't fail the request, just continue without role
    next();
  }
};

/**
 * Check if user has specific permission based on role
 * @param {string} permission - Permission to check
 * @param {string} userRole - User's role
 */
const hasPermission = (permission, userRole) => {
  const permissions = {
    admin: [
      'upload_signatures',
      'verify_documents',
      'sign_documents',
      'view_history',
      'manage_users',
      'view_dashboard',
      'change_password'
    ],
    user: [
      'view_history',
      'view_dashboard',
      'change_password'
      // Note: 'upload_signatures', 'verify_documents', and 'sign_documents' are NOT included for users
    ]
  };

  return permissions[userRole]?.includes(permission) || false;
};

/**
 * Middleware to check specific permission
 * @param {string} permission - Required permission
 */
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.userId;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: 'غير مخول للوصول'
        });
      }

      // Get user role if not already set
      if (!req.user.role) {
        const result = await query('SELECT role FROM users WHERE id = $1', [userId]);
        if (result.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: 'المستخدم غير موجود'
          });
        }
        req.user.role = result.rows[0].role;
      }

      // Check permission
      if (!hasPermission(permission, req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'ليس لديك صلاحية لتنفيذ هذا الإجراء'
        });
      }

      next();
      
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في التحقق من الصلاحيات'
      });
    }
  };
};

module.exports = {
  requireRole,
  requireAdmin,
  requireUser,
  addUserRole,
  hasPermission,
  requirePermission
};
