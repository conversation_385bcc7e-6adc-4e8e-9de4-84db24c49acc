const express = require('express');
const {
  upload,
  uploadWithTracking,
  testPDFUpload,
  signDocument,
  getDocuments,
  getDocument,
  downloadDocument,
  viewDocument,
  verifyDocumentBySerial
} = require('../controllers/documentController');
const { authenticateToken } = require('../middleware/auth');
const { createChunkedUpload, handleChunkUpload, checkUploadStatus } = require('../middleware/chunkedUpload');
const {
  checkDocumentAccess,
  requireDocumentOwnership,
  filterUserDocuments,
  documentRateLimit
} = require('../middleware/documentAuth');

const router = express.Router();

// Public routes (no authentication required)
// Verify document by serial number (public endpoint for verification)
router.get('/verify/:serialNumber', verifyDocumentBySerial);

// All other document routes require authentication
router.use(authenticateToken);

// Test PDF upload endpoint for debugging
router.post('/test-upload', upload, (req, res) => {
  try {
    const file = req.file;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Get detailed file information
    const fileInfo = {
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      bufferLength: file.buffer ? file.buffer.length : 0,
      header: file.buffer ? file.buffer.slice(0, 50).toString('ascii') : 'No buffer',
      headerHex: file.buffer ? file.buffer.slice(0, 20).toString('hex') : 'No buffer'
    };

    console.log('Test upload file info:', fileInfo);

    res.json({
      message: 'File upload test successful',
      fileInfo
    });
  } catch (error) {
    console.error('Test upload error:', error);
    res.status(500).json({ error: 'Test upload failed', details: error.message });
  }
});

// Test PDF upload (for debugging) - authenticated with rate limiting
router.post('/test-pdf', authenticateToken, documentRateLimit(50), upload, testPDFUpload);

// Sign document with enhanced upload tracking and rate limiting
router.post('/sign', authenticateToken, documentRateLimit(20), uploadWithTracking, signDocument);

// Mail functionality routes (must be before parameterized routes)
const { requirePermission } = require('../middleware/roleAuth');
const { query } = require('../models/database');
const { generateSerialNumber } = require('../services/pdfService');

// Upload document for review
router.post('/upload-for-review', authenticateToken, upload, async (req, res) => {
  try {
    const { user } = req;
    const { notes } = req.body;

    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    if (req.file.mimetype !== 'application/pdf') {
      return res.status(400).json({ error: 'Only PDF files are allowed' });
    }

    // Save the file to disk since memoryStorage doesn't provide a path
    const fs = require('fs').promises;
    const path = require('path');
    const crypto = require('crypto');

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(__dirname, '../../uploads/pending');
    await fs.mkdir(uploadsDir, { recursive: true });

    // Generate unique filename
    const fileExtension = path.extname(req.file.originalname);
    const uniqueFilename = `${user.id}_${Date.now()}_${crypto.randomBytes(8).toString('hex')}${fileExtension}`;
    const filePath = path.join(uploadsDir, uniqueFilename);

    // Save file buffer to disk
    await fs.writeFile(filePath, req.file.buffer);

    // Store the document in pending_documents table
    const result = await query(
      `INSERT INTO pending_documents (
        original_filename, file_path, file_size, uploaded_by, uploader_email, notes, status
      ) VALUES ($1, $2, $3, $4, $5, $6, 'pending') RETURNING *`,
      [
        req.file.originalname,
        filePath,
        req.file.size,
        user.id,
        user.email,
        notes || null
      ]
    );

    res.json({
      success: true,
      message: 'تم رفع المستند بنجاح للمراجعة',
      document: result.rows[0]
    });
  } catch (error) {
    console.error('Upload for review error:', error);
    res.status(500).json({ error: 'فشل في رفع المستند' });
  }
});

// Get pending documents (admin only)
router.get('/pending', authenticateToken, requirePermission('sign_documents'), async (req, res) => {
  try {
    const result = await query(
      `SELECT pd.*, u.email as uploader_email
       FROM pending_documents pd
       JOIN users u ON pd.uploaded_by = u.id
       ORDER BY pd.uploaded_at DESC`
    );

    res.json({
      success: true,
      documents: result.rows
    });
  } catch (error) {
    console.error('Get pending documents error:', error);
    res.status(500).json({ error: 'فشل في تحميل المستندات المعلقة' });
  }
});

// Sign pending document (admin only)
router.post('/:id/sign-pending', authenticateToken, requirePermission('sign_documents'), async (req, res) => {
  try {
    const { id } = req.params;
    const { user } = req;

    // Get the pending document
    const pendingResult = await query(
      'SELECT * FROM pending_documents WHERE id = $1 AND status = $2',
      [id, 'pending']
    );

    if (pendingResult.rows.length === 0) {
      return res.status(404).json({ error: 'المستند غير موجود أو تم توقيعه مسبقاً' });
    }

    const pendingDoc = pendingResult.rows[0];

    // Get admin's signature
    const signatureResult = await query(
      'SELECT * FROM signatures WHERE user_id = $1 ORDER BY created_at DESC LIMIT 1',
      [user.id]
    );

    if (signatureResult.rows.length === 0) {
      return res.status(400).json({ error: 'لا يوجد توقيع مرفوع للمدير' });
    }

    const signature = signatureResult.rows[0];

    // Generate serial number and digital signature
    const serialNumber = generateSerialNumber();

    // Create a simple digital signature for mail documents
    const crypto = require('crypto');
    const digitalSignature = crypto.createHash('sha256')
      .update(`${pendingDoc.original_filename}-${user.id}-${serialNumber}-${Date.now()}`)
      .digest('hex');

    // Move to signed_documents table
    const signedResult = await query(
      `INSERT INTO signed_documents (
        original_filename, file_path, file_size, signed_date,
        user_id, user_email, serial_number, digital_signature
      ) VALUES ($1, $2, $3, NOW(), $4, $5, $6, $7) RETURNING *`,
      [
        pendingDoc.original_filename,
        pendingDoc.file_path, // Use original path for now
        pendingDoc.file_size,
        user.id,
        user.email,
        serialNumber,
        digitalSignature
      ]
    );

    // Update pending document status
    await query(
      'UPDATE pending_documents SET status = $1, signed_at = NOW(), signed_by = $2 WHERE id = $3',
      ['signed', user.id, id]
    );

    res.json({
      success: true,
      message: 'تم توقيع المستند بنجاح',
      document: signedResult.rows[0]
    });
  } catch (error) {
    console.error('Sign pending document error:', error);
    res.status(500).json({ error: 'فشل في توقيع المستند' });
  }
});

// Reject pending document (admin only)
router.post('/:id/reject', authenticateToken, requirePermission('sign_documents'), async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const { user } = req;

    const result = await query(
      'UPDATE pending_documents SET status = $1, rejection_reason = $2, rejected_at = NOW(), rejected_by = $3 WHERE id = $4 AND status = $5 RETURNING *',
      ['rejected', reason, user.id, id, 'pending']
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المستند غير موجود أو تم التعامل معه مسبقاً' });
    }

    res.json({
      success: true,
      message: 'تم رفض المستند',
      document: result.rows[0]
    });
  } catch (error) {
    console.error('Reject document error:', error);
    res.status(500).json({ error: 'فشل في رفض المستند' });
  }
});

// Get all documents for user (filtered by ownership)
router.get('/', authenticateToken, filterUserDocuments, getDocuments);

// Get specific document (with access control)
router.get('/:documentId', authenticateToken, checkDocumentAccess('VIEW'), getDocument);

// Download signed document (with access control and audit logging)
router.get('/:documentId/download', authenticateToken, checkDocumentAccess('DOWNLOAD'), downloadDocument);

// View document in browser (with access control and audit logging)
router.get('/:documentId/view', authenticateToken, checkDocumentAccess('VIEW'), viewDocument);

// Chunked upload for very large files
const chunkedUpload = createChunkedUpload({
  destination: './uploads/chunks',
  maxChunkSize: 10 * 1024 * 1024, // 10MB chunks
  allowedMimeTypes: ['application/pdf'],
  allowedExtensions: ['.pdf']
});

router.post('/sign/chunk', handleChunkUpload, chunkedUpload.single('chunk'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No chunk uploaded' });
    }

    if (req.file.isComplete) {
      // File assembly complete, proceed with signing
      // This would need to be integrated with the signDocument function
      res.json({
        success: true,
        isComplete: true,
        message: 'File uploaded and assembled successfully',
        filePath: req.file.path
      });
    } else {
      // Chunk uploaded successfully, but file not complete
      res.json({
        success: true,
        isComplete: false,
        uploadedChunks: req.file.uploadedChunks,
        totalChunks: req.file.totalChunks,
        message: `Chunk ${req.file.uploadedChunks}/${req.file.totalChunks} uploaded successfully`
      });
    }
  } catch (error) {
    console.error('Chunked upload error:', error);
    res.status(500).json({ error: 'Chunked upload failed' });
  }
});

// Check upload status
router.get('/upload-status/:fileId', checkUploadStatus);

module.exports = router;
