import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

const Navbar: React.FC = () => {
  const { user, logout, hasPermission } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200" dir="rtl">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-2">
          {/* Logo and Brand Section */}
          <Link
            to="/"
            className="flex items-center space-x-reverse space-x-2 hover:opacity-80 transition-opacity duration-200"
          >
            <img
              src="/logo.svg"
              alt="نظام التوقيع الإلكتروني"
              className="h-8 w-8 flex-shrink-0"
            />
            <span className="text-base font-bold text-primary-600 font-['Almarai']">
              {t.nav.title}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-reverse space-x-4">
            {user ? (
              <>
                <Link
                  to="/dashboard"
                  className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                >
                  {t.nav.dashboard}
                </Link>
                {hasPermission('sign_documents') && (
                  <Link
                    to="/document-signing"
                    className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                  >
                    {t.nav.documentSigning}
                  </Link>
                )}
                {hasPermission('view_history') && user?.role === 'admin' && (
                  <Link
                    to="/history"
                    className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                  >
                    {t.nav.history}
                  </Link>
                )}
                <Link
                  to="/mail"
                  className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                >
                  البريد
                </Link>
                {hasPermission('sign_documents') && (
                  <Link
                    to="/admin/document-signing"
                    className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                  >
                    توقيع المستندات
                  </Link>
                )}
                {hasPermission('view_history') && user?.role === 'admin' && (
                  <Link
                    to="/admin/records"
                    className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                  >
                    السجل
                  </Link>
                )}
                <Link
                  to="/users"
                  className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                >
                  المستخدمين
                </Link>
                {hasPermission('verify_documents') && (
                  <Link
                    to="/verify"
                    className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-sm"
                  >
                    التحقق
                  </Link>
                )}

                {/* User Info and Logout */}
                <div className="flex items-center space-x-reverse space-x-3 mr-4 pr-4 border-r border-gray-200">
                  <div className="flex items-center space-x-reverse space-x-2">
                    <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-bold text-xs font-['Almarai']">
                        {user.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <span className="text-gray-600 font-['Almarai'] text-xs hidden xl:block">
                      {user.email}
                    </span>
                  </div>
                  <Link
                    to="/settings"
                    className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium text-xs"
                  >
                    الإعدادات
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded transition-colors duration-200 font-['Almarai'] font-medium text-xs"
                  >
                    {t.nav.logout}
                  </button>
                </div>
              </>
            ) : (
              <>
                <Link
                  to="/login"
                  className="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-['Almarai'] font-medium"
                >
                  {t.auth.login}
                </Link>
                <Link
                  to="/register"
                  className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium shadow-sm"
                >
                  {t.auth.register}
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              onClick={toggleMobileMenu}
              className="text-gray-600 hover:text-primary-600 focus:outline-none focus:text-primary-600 transition-colors duration-200"
              aria-label="فتح القائمة"
            >
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 bg-gray-50">
            <div className="px-4 py-2 space-y-2">
              {user ? (
                <>
                  {/* User Info */}
                  <div className="flex items-center space-x-reverse space-x-3 pb-3 border-b border-gray-200">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-primary-600 font-bold font-['Almarai']">
                        {user.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-gray-800 font-['Almarai'] font-medium text-sm">مرحباً</p>
                      <p className="text-gray-600 font-['Almarai'] text-xs">{user.email}</p>
                    </div>
                  </div>

                  {/* Navigation Links */}
                  <Link
                    to="/dashboard"
                    className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {t.nav.dashboard}
                  </Link>

                  {hasPermission('sign_documents') && (
                    <Link
                      to="/document-signing"
                      className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {t.nav.documentSigning}
                    </Link>
                  )}
                  {hasPermission('view_history') && user?.role === 'admin' && (
                    <Link
                      to="/history"
                      className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {t.nav.history}
                    </Link>
                  )}
                  <Link
                    to="/mail"
                    className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    البريد
                  </Link>
                  {hasPermission('sign_documents') && (
                    <Link
                      to="/admin/document-signing"
                      className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      توقيع المستندات
                    </Link>
                  )}
                  {hasPermission('view_history') && user?.role === 'admin' && (
                    <Link
                      to="/admin/records"
                      className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      السجل
                    </Link>
                  )}
                  <Link
                    to="/users"
                    className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    المستخدمين
                  </Link>
                  {hasPermission('verify_documents') && (
                    <Link
                      to="/verify"
                      className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      التحقق
                    </Link>
                  )}
                  <Link
                    to="/settings"
                    className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    الإعدادات
                  </Link>

                  {/* Logout Button */}
                  <div className="pt-3 border-t border-gray-200">
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsMobileMenuOpen(false);
                      }}
                      className="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium shadow-sm"
                    >
                      {t.nav.logout}
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="block text-gray-700 hover:text-primary-600 hover:bg-primary-50 px-3 py-2 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {t.auth.login}
                  </Link>
                  <Link
                    to="/register"
                    className="block bg-primary-500 hover:bg-primary-600 text-white px-4 py-3 rounded-lg transition-colors duration-200 font-['Almarai'] font-medium text-center shadow-sm"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {t.auth.register}
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
