import React, { useState, useEffect } from 'react';
import { useAuth } from '../services/AuthContext';
import { documentAPI } from '../services/api';

interface PendingDocument {
  id: number;
  original_filename: string;
  file_size: number;
  uploaded_by: string;
  uploader_email: string;
  notes: string;
  status: 'pending' | 'signed' | 'rejected';
  uploaded_at: string;
  signed_at?: string;
  signed_by?: string;
  rejected_at?: string;
  rejected_by?: string;
  rejection_reason?: string;
}

const AdminDocumentSigning: React.FC = () => {
  const { hasPermission } = useAuth();
  const [pendingDocuments, setPendingDocuments] = useState<PendingDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [processingId, setProcessingId] = useState<number | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState<number | null>(null);

  const fetchPendingDocuments = async () => {
    try {
      setLoading(true);
      const response = await documentAPI.getPendingDocuments();
      setPendingDocuments(response.data.documents || []);
    } catch (error) {
      console.error('Error fetching pending documents:', error);
      setError('فشل في تحميل المستندات المعلقة');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPendingDocuments();
  }, []);

  const handleSignDocument = async (documentId: number) => {
    try {
      setProcessingId(documentId);
      setError('');
      setSuccess('');

      await documentAPI.signPendingDocument(documentId.toString());
      
      setSuccess('تم توقيع المستند بنجاح');
      fetchPendingDocuments(); // Refresh the list
    } catch (error: any) {
      console.error('Error signing document:', error);
      setError(error.response?.data?.error || 'فشل في توقيع المستند');
    } finally {
      setProcessingId(null);
    }
  };

  const handleRejectDocument = async (documentId: number) => {
    try {
      setProcessingId(documentId);
      setError('');
      setSuccess('');

      await documentAPI.rejectPendingDocument(documentId.toString(), rejectionReason);
      
      setSuccess('تم رفض المستند');
      setShowRejectModal(null);
      setRejectionReason('');
      fetchPendingDocuments(); // Refresh the list
    } catch (error: any) {
      console.error('Error rejecting document:', error);
      setError(error.response?.data?.error || 'فشل في رفض المستند');
    } finally {
      setProcessingId(null);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Check if user has admin permissions
  if (!hasPermission('sign_documents')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50" dir="rtl">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2 font-['Almarai']">
            غير مخول للوصول
          </h2>
          <p className="text-gray-600 font-['Almarai']">
            ليس لديك صلاحية للوصول إلى صفحة توقيع المستندات
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50" dir="rtl">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 font-['Almarai']">
            توقيع المستندات
          </h1>
          <p className="mt-2 text-gray-600 font-['Almarai']">
            إدارة المستندات المعلقة للمراجعة والتوقيع
          </p>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg font-['Almarai']">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg font-['Almarai']">
            {success}
          </div>
        )}

        {/* Documents List */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 font-['Almarai']">
              المستندات المعلقة ({pendingDocuments.length})
            </h2>
          </div>

          {pendingDocuments.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2 font-['Almarai']">
                لا توجد مستندات معلقة
              </h3>
              <p className="text-gray-500 font-['Almarai']">
                لا توجد مستندات تحتاج إلى مراجعة في الوقت الحالي
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {pendingDocuments.map((document) => (
                <div key={document.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-reverse space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate font-['Almarai']">
                            {document.original_filename}
                          </p>
                          <div className="mt-1 flex items-center space-x-reverse space-x-4 text-sm text-gray-500">
                            <span className="font-['Almarai']">
                              {document.uploader_email}
                            </span>
                            <span className="font-['Almarai']">
                              {formatFileSize(document.file_size)}
                            </span>
                            <span className="font-['Almarai']">
                              {formatDate(document.uploaded_at)}
                            </span>
                          </div>
                          {document.notes && (
                            <p className="mt-2 text-sm text-gray-600 font-['Almarai']">
                              <span className="font-medium">ملاحظات:</span> {document.notes}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-reverse space-x-2">
                      <button
                        onClick={() => handleSignDocument(document.id)}
                        disabled={processingId === document.id}
                        className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 font-['Almarai']"
                      >
                        {processingId === document.id ? 'جاري التوقيع...' : 'توقيع'}
                      </button>
                      <button
                        onClick={() => setShowRejectModal(document.id)}
                        disabled={processingId === document.id}
                        className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 font-['Almarai']"
                      >
                        رفض
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Reject Modal */}
        {showRejectModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <h3 className="text-lg font-medium text-gray-900 font-['Almarai']">
                  رفض المستند
                </h3>
                <div className="mt-4">
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    placeholder="يرجى إدخال سبب الرفض..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 font-['Almarai']"
                    rows={4}
                    required
                  />
                </div>
                <div className="flex justify-center space-x-reverse space-x-4 mt-6">
                  <button
                    onClick={() => handleRejectDocument(showRejectModal)}
                    disabled={!rejectionReason.trim() || processingId === showRejectModal}
                    className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 font-['Almarai']"
                  >
                    {processingId === showRejectModal ? 'جاري الرفض...' : 'تأكيد الرفض'}
                  </button>
                  <button
                    onClick={() => {
                      setShowRejectModal(null);
                      setRejectionReason('');
                    }}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 font-['Almarai']"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDocumentSigning;
