<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Access Control</title>
    <style>
        body {
            font-family: 'Alma<PERSON>', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            font-size: 12px;
        }
        .user-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .current-user {
            border-color: #28a745;
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Test User Access Control</h1>
        
        <div class="section info">
            <h3>Current Status</h3>
            <div id="currentStatus">Loading...</div>
        </div>

        <div class="section">
            <h3>Test Users</h3>
            <div class="user-card">
                <h4>👤 Regular User</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> password123</p>
                <p><strong>Expected Role:</strong> user</p>
                <p><strong>Should See Admin Pages:</strong> ❌ NO</p>
                <button onclick="loginAs('<EMAIL>', 'password123')">Login as Regular User</button>
            </div>
            
            <div class="user-card">
                <h4>👑 Admin User</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> admin123</p>
                <p><strong>Expected Role:</strong> admin</p>
                <p><strong>Should See Admin Pages:</strong> ✅ YES</p>
                <button onclick="loginAs('<EMAIL>', 'admin123')">Login as Admin User</button>
            </div>
            
            <div class="user-card">
                <h4>👑 Your Admin Account</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Expected Role:</strong> admin</p>
                <p><strong>Should See Admin Pages:</strong> ✅ YES</p>
                <p><em>You are currently logged in with this account</em></p>
            </div>
        </div>

        <div class="section">
            <h3>Actions</h3>
            <button onclick="checkCurrentUser()">Check Current User</button>
            <button onclick="testPermissions()">Test Permissions</button>
            <button onclick="testNavigation()">Test Navigation Logic</button>
            <button onclick="clearAuth()" class="danger">Logout (Clear Auth)</button>
        </div>

        <div class="section">
            <h3>Results</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `section ${type}`;
            div.innerHTML = `<pre>${message}</pre>`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('currentStatus');
            status.className = `section ${type}`;
            status.innerHTML = message;
        }

        async function loginAs(email, password) {
            try {
                updateStatus(`Logging in as ${email}...`, 'warning');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    
                    log(`✅ Login Successful for ${email}:
Role: ${data.user.role}
ID: ${data.user.id}
Expected Admin Access: ${data.user.role === 'admin' ? 'YES' : 'NO'}`, 'success');
                    
                    updateStatus(`Logged in as ${email} (${data.user.role})`, 'success');
                    
                    // Automatically test permissions after login
                    setTimeout(() => {
                        testPermissions();
                        testNavigation();
                    }, 1000);
                } else {
                    log(`❌ Login Failed for ${email}: ${JSON.stringify(data, null, 2)}`, 'error');
                    updateStatus('Login failed', 'error');
                }
            } catch (error) {
                log(`❌ Login Error for ${email}: ${error.message}`, 'error');
                updateStatus('Login error', 'error');
            }
        }

        function checkCurrentUser() {
            const token = localStorage.getItem('token');
            const userStr = localStorage.getItem('user');
            
            if (!token || !userStr) {
                log('❌ No user logged in', 'error');
                updateStatus('Not logged in', 'error');
                return;
            }

            try {
                const user = JSON.parse(userStr);
                
                log(`✅ Current User:
Email: ${user.email}
Role: ${user.role}
ID: ${user.id}
Created: ${user.createdAt}
Language: ${user.language}
Text Direction: ${user.textDirection}`, 'success');

                updateStatus(`Logged in as ${user.email} (${user.role})`, 'success');
            } catch (error) {
                log(`❌ Error parsing user data: ${error.message}`, 'error');
                updateStatus('Invalid user data', 'error');
            }
        }

        function testPermissions() {
            const userStr = localStorage.getItem('user');
            
            if (!userStr) {
                log('❌ No user data found', 'error');
                return;
            }

            try {
                const user = JSON.parse(userStr);
                
                // Simulate the exact frontend permission logic
                const permissions = {
                    admin: [
                        'upload_signatures',
                        'verify_documents',
                        'sign_documents',
                        'view_history',
                        'manage_users',
                        'view_dashboard',
                        'change_password'
                    ],
                    user: [
                        'view_history',
                        'view_dashboard',
                        'change_password'
                    ]
                };

                const userPermissions = permissions[user.role] || [];
                const hasSignDocuments = userPermissions.includes('sign_documents');
                const hasViewHistory = userPermissions.includes('view_history');
                const isAdmin = user.role === 'admin';

                log(`🔍 Permission Test Results for ${user.email}:
User Role: ${user.role}
Available Permissions: ${userPermissions.join(', ')}

Critical Permission Checks:
- hasPermission('sign_documents'): ${hasSignDocuments}
- hasPermission('view_history'): ${hasViewHistory}
- isAdmin(): ${isAdmin}

Expected Results for ${user.role}:
- sign_documents: ${user.role === 'admin' ? 'YES' : 'NO'}
- view_history: YES (both roles have this)
- isAdmin(): ${user.role === 'admin' ? 'YES' : 'NO'}

Status: ${
    (user.role === 'admin' && hasSignDocuments && isAdmin) ||
    (user.role === 'user' && !hasSignDocuments && !isAdmin)
    ? '✅ CORRECT' : '❌ INCORRECT - SECURITY ISSUE!'
}`, hasSignDocuments && user.role === 'user' ? 'error' : 'success');

            } catch (error) {
                log(`❌ Error testing permissions: ${error.message}`, 'error');
            }
        }

        function testNavigation() {
            const userStr = localStorage.getItem('user');
            
            if (!userStr) {
                log('❌ No user data found', 'error');
                return;
            }

            try {
                const user = JSON.parse(userStr);
                
                // Simulate the exact navigation logic from Navbar.tsx
                const permissions = {
                    admin: [
                        'upload_signatures',
                        'verify_documents',
                        'sign_documents',
                        'view_history',
                        'manage_users',
                        'view_dashboard',
                        'change_password'
                    ],
                    user: [
                        'view_history',
                        'view_dashboard',
                        'change_password'
                    ]
                };

                const hasPermission = (permission) => {
                    return permissions[user.role]?.includes(permission) || false;
                };

                const isAdmin = () => {
                    return user.role === 'admin';
                };

                // Test navigation visibility logic
                const documentSigningVisible = hasPermission('sign_documents');
                const recordsVisible = hasPermission('view_history') && isAdmin();

                log(`🧭 Navigation Visibility Test for ${user.email}:

Navigation Links:
- "توقيع المستندات" (Document Signing): ${documentSigningVisible ? 'VISIBLE ✅' : 'HIDDEN ❌'}
  Logic: hasPermission('sign_documents') = ${hasPermission('sign_documents')}
  
- "السجل" (Records): ${recordsVisible ? 'VISIBLE ✅' : 'HIDDEN ❌'}
  Logic: hasPermission('view_history') && isAdmin() = ${hasPermission('view_history')} && ${isAdmin()} = ${recordsVisible}

Expected for ${user.role}:
- Document Signing: ${user.role === 'admin' ? 'VISIBLE' : 'HIDDEN'}
- Records: ${user.role === 'admin' ? 'VISIBLE' : 'HIDDEN'}

Status: ${
    (user.role === 'admin' && documentSigningVisible && recordsVisible) ||
    (user.role === 'user' && !documentSigningVisible && !recordsVisible)
    ? '✅ CORRECT' : '❌ INCORRECT - SECURITY ISSUE!'
}`, 
    (user.role === 'user' && (documentSigningVisible || recordsVisible)) ? 'error' : 'success');

            } catch (error) {
                log(`❌ Error testing navigation: ${error.message}`, 'error');
            }
        }

        function clearAuth() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            log('🗑️ Authentication data cleared. You are now logged out.', 'warning');
            updateStatus('Not logged in', 'error');
        }

        // Auto-check current user on page load
        window.onload = function() {
            checkCurrentUser();
        };
    </script>
</body>
</html>
